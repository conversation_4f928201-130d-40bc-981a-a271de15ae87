<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentReceipt\GetAllRequestPaymentReceiptAction;

use App\Modules\CollectDebt\Model\RequestPaymentReceipt;
use App\Modules\CollectDebt\Requests\RequestPaymentReceipt\GetAllRequestPaymentReceiptRequest;

class GetAllRequestPaymentReceiptAction
{
	public function run(GetAllRequestPaymentReceiptRequest $request)
	{
		$filters = $request->json('data');

		$query = RequestPaymentReceipt::query();

		if (!empty($filters['partner_code'])) {
			$query->where('partner_code', '=', $filters['partner_code']);
		}

		if (!empty($filters['contract_code'])) {
			$query->where('contract_code', '=', $filters['contract_code']);
		}

		if (!empty($filters['status'])) {
			$query->where('status', $filters['status']);
		}

		if (!empty($filters['payment_method_code'])) {
			$query->where('payment_method_code', $filters['payment_method_code']);
		}

		if (!empty($filters['partner_transaction_id'])) {
			$query->where('partner_transaction_id', $filters['partner_transaction_id']);
		}

		if (!empty($filters['partner_request_id'])) {
			$query->whereHas('requestPaymentGuide', function ($q) use ($filters) {
				return $q->where('partner_request_id', $filters['partner_request_id']);
			});
		}
	

		$query->with([
			'requestPaymentGuideMerchant:id,merchant_id,request_payment_guide_id,merchant_name,representation_name,email,mobile,address,passport',
			'requestPaymentGuide:id,contract_code,partner_code,amount,time_created,time_expired,partner_contract_code,contract_amount',
			'requestPayment:partner_request_id,amount_request'
		])->latest('request_payment_receipt.id');

		return $query->paginate(
			$request->json('data.limit', 20),
			[
				'id',
				'contract_code',
				'partner_request_id',
				'partner_transaction_id',
				'payment_method_code',
				'amount',
				'status',
				'time_created',
				'time_created_request',
				'request_payment_guide_id'
			],
			'page',
			$request->json('data.page', 1)
		);
	}
} // End class 