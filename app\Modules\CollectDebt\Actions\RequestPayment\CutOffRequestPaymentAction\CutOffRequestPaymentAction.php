<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\CutOffRequestPaymentAction;

use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebtGateway\Repositories\CollectDebtGatewayRepository;
use App\Modules\CollectDebt\DTOs\RequestPaymentReceipt\CreateRequestPaymentReceiptDTO;
use App\Modules\CollectDebt\Actions\RequestPayment\CheckRequestPaymentAction\CheckRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPayment\CancelRequestPaymentAction\CancelRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPaymentReceipt\CreateRequestPaymentReceiptAction\CreateRequestPaymentReceiptAction;

class CutOffRequestPaymentAction
{
	private array $__processedIds = [];

	private array $__excludedIds = [];

	public CollectDebtGatewayRepository $mposCollectDebtGatewayRepo;

	public function __construct(CollectDebtGatewayRepository $mposCollectDebtGatewayRepo)
	{
		$this->mposCollectDebtGatewayRepo = $mposCollectDebtGatewayRepo;
	}


	public function run()
	{
		try {
			for ($i = 1; $i < 40; $i++) {

				$requestPayment = $this->processCutOffRequestPayment();
				if ($requestPayment == 'EMPTY') {
					break;
				}

				$this->__processedIds[] = $requestPayment->partner_request_id;
			}
		} catch (\Throwable $th) {
			throw $th;
		}

		return $this->__processedIds;
	}

	public function processCutOffRequestPayment()
	{
		$requestPayment = RequestPayment::query()
			->where('status_payment', '!=', CollectDebtEnum::RP_STT_PAYMENT_RECEIVED)
			->where('time_expired', '<=', now()->timestamp)
			->doesntHave('requestPaymentReceipt');

		if (!empty($this->__excludedIds)) {
			$requestPayment = $requestPayment->whereNotIn('id', $this->__excludedIds);
		}

		$requestPayment = $requestPayment->orderByRaw(request('orderByRaw', 'id asc'))->first();

		if (!$requestPayment) {
			return 'EMPTY';
		}

		$this->__excludedIds[] = $requestPayment->id;

		$checkResult = app(CheckRequestPaymentAction::class)->executeCheck($requestPayment);
	
		// Dựa vào status trả về để xử lý làm gì tiếp
		$debtStatus = app(CheckRequestPaymentAction::class)->getDebitCmdStatus($checkResult);

		switch ($debtStatus) {
			// Hủy thành công -> giải phóng lệnh trích
			case 'CANCEL':
			case 'NOTFOUND':
				$dto = CreateRequestPaymentReceiptDTO::fromRequestPayment($requestPayment, 0, CollectDebtEnum::RP_RECEIPT_APPROVED);
				app(CreateRequestPaymentReceiptAction::class)->run($dto);
				break;

			// chưa có kết quả trích
			case 'PENDING':
				// Không có thông tin -> gọi hủy để kết thúc
				$cancelResult = app(CancelRequestPaymentAction::class)->run($requestPayment);
				if (CancelRequestPaymentAction::isCancelSuccess($cancelResult)) {
					$dto = CreateRequestPaymentReceiptDTO::fromRequestPayment($requestPayment, 0, CollectDebtEnum::RP_RECEIPT_APPROVED);
					app(CreateRequestPaymentReceiptAction::class)->run($dto);
				}
				break;

			// lệnh hết hạn -> gọi cancel và không quan tâm cancel được hay không
			case 'EXPIRED':
				$cancelResult = app(CancelRequestPaymentAction::class)->run($requestPayment);
				$dto = CreateRequestPaymentReceiptDTO::fromRequestPayment($requestPayment, 0, CollectDebtEnum::RP_RECEIPT_APPROVED);
				app(CreateRequestPaymentReceiptAction::class)->run($dto);
				break;

			// gọi sang mpos có vấn đề gì đó mà không được
			case 'TIMEOUT':
				$cancelResult = app(CancelRequestPaymentAction::class)->run($requestPayment);
				if (CancelRequestPaymentAction::isCancelSuccess($cancelResult)) {
					$dto = CreateRequestPaymentReceiptDTO::fromRequestPayment($requestPayment, 0, CollectDebtEnum::RP_RECEIPT_APPROVED);
					app(CreateRequestPaymentReceiptAction::class)->run($dto);
				}
				break;

			// trích thành công -> giải phóng lệnh trích
			case 'APPROVED':
				$dto = CreateRequestPaymentReceiptDTO::fromRequestPayment(
					$requestPayment, 
					$checkResult['data']['data']['debtRecoveryAmount'],
					CollectDebtEnum::RP_RECEIPT_APPROVED
				);
				app(CreateRequestPaymentReceiptAction::class)->run($dto);
				break;

			// những trường hợp khác, gọi cancel rồi bắt trên kết quả
			default:
				$cancelResult = app(CancelRequestPaymentAction::class)->run($requestPayment);
				if (CancelRequestPaymentAction::isCancelSuccess($cancelResult)) {
					$dto = CreateRequestPaymentReceiptDTO::fromRequestPayment($requestPayment, 0, CollectDebtEnum::RP_RECEIPT_APPROVED);
					app(CreateRequestPaymentReceiptAction::class)->run($dto);
				}
				break;
		}

		return $requestPayment;
	}
} // End class