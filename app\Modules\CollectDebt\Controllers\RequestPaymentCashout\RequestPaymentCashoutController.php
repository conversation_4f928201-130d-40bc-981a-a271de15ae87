<?php

namespace App\Modules\CollectDebt\Controllers\RequestPaymentCashout;

use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Actions\RequestPaymentCashout\CreateRequestPaymentCashoutAction\CreateRequestPaymentCashoutAction;

class RequestPaymentCashoutController extends Controller
{
	public function CreateRequestPaymentCashout()
	{
		$result = app(CreateRequestPaymentCashoutAction::class)->run();
		return $this->successResponse($result);
	}
} // End class