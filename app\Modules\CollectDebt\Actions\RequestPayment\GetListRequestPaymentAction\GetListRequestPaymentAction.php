<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\GetListRequestPaymentAction;

use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Requests\RequestPayment\GetListRequestPaymentRequest;

class GetListRequestPaymentAction
{
	public function run(GetListRequestPaymentRequest $request)
	{
		$requestPayments = RequestPayment::query();

		if (!empty($request->json('data.contract_code'))) {
			$requestPayments = $requestPayments->where('contract_code', $request->json('data.contract_code'));
		}

		if (!empty($request->json('data.partner_code'))) {
			$requestPayments = $requestPayments->where('partner_code', $request->json('data.partner_code'));
		}

		if (!empty($request->json('data.partner_request_id'))) {
			$requestPayments = $requestPayments->whereHas('requestPaymentGuide', function ($q) use ($request) {
				return $q->where('partner_request_id', trim($request->json('data.partner_request_id')));
			});
		}

		$requestPayments = $requestPayments->with([
			'requestPaymentGuideMerchant:id,merchant_id,request_payment_guide_id,merchant_name,representation_name,email,mobile,address,passport',
			'requestPaymentGuide:id,contract_code,partner_code,amount,time_created,time_expired,partner_contract_code,contract_amount',
		])
		->latest('id')
		->paginate(
			$request->json('data.limit', 20),
			[
				'id',
				'contract_code',
				'partner_request_id',
				'partner_transaction_id',
				'partner_request_id',
				'payment_method_code',
				'amount_request',
				'amount_receiver',
				'status',
				'status_payment',
				'time_created',
				'time_receivered',
				'time_expired',
				'time_sended',
				'time_receivered',
				'time_canceled',
				'time_checked',
				'request_payment_guide_id',
			],
			'page',
			$request->json('data.page', 1)
		);
		return $requestPayments;
	}
} // End class