<?php

namespace App\Modules\CollectDebt\Model;

use Illuminate\Database\Eloquent\Model;
use App\Modules\CollectDebt\Model\RequestPayment;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RequestPaymentType extends Model
{
  protected $table = 'request_payment_type';
  public $timestamps = false;
  protected $appends = [];
  protected $guarded = [];

	public function requestPayment(): BelongsTo {
		return $this->belongsTo(RequestPayment::class, 'request_payment_id', 'id');
	}
} // End class
