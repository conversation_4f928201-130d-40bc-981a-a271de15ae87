<?php 
namespace App\Modules\CollectDebt\Actions\RequestPaymentCashout\CreateRequestPaymentCashoutAction;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use App\Modules\CollectDebt\Model\RequestPaymentCashout;

class CreateRequestPaymentCashoutAction {
	private array $__processedIds = [];

	private array $__excludedIds = [];

	public function run()
	{
		for ($i = 1; $i < 40; $i++) {
			try {
				$requestPaymentGuide = $this->processCreateCasout();
				if ($requestPaymentGuide == 'EMPTY') {
					break;
				}

				$this->__processedIds[] = $requestPaymentGuide->id;
			} catch (\Throwable $th) {
				throw $th;
			}
		}

		return $this->__processedIds;
	}

	public function processCreateCasout()
	{
		$requestPaymentGuide = RequestPaymentGuide::query()->where([
			'status' => CollectDebtEnum::RPG_CLOSED,
			'status_cashout' => CollectDebtEnum::RPG_STT_CASHOUT_UNCREATE
		]);

		if (!empty($this->__excludedIds)) {
			$requestPaymentGuide = $requestPaymentGuide->whereNotIn('id', $this->__excludedIds);
		}

		$requestPaymentGuide = $requestPaymentGuide->orderByRaw(request('orderByRaw', 'id asc'))->first();

		if (!$requestPaymentGuide) {
			return 'EMPTY';
		}

		$this->__excludedIds[] = $requestPaymentGuide->id;

		$wasUpdateCreating = RequestPaymentGuide::query()
			->where('id', $requestPaymentGuide->id)
			->where('status_cashout', CollectDebtEnum::RPG_STT_CASHOUT_UNCREATE)
			->update([
				'status_cashout' => CollectDebtEnum::RPG_STT_CASHOUT_CREATING,
				'time_updated' => now()->timestamp,
			]);

		if (!$wasUpdateCreating) {
			throw new BusinessException('Loi khong update duoc thanh dang tao yc cashout');
		}

		DB::beginTransaction();
		try {
			$cashout = RequestPaymentCashout::query()->forceCreate([
				'request_payment_guide_id' => $requestPaymentGuide->id,
				'partner_request_id' => $requestPaymentGuide->partner_request_id,
				'amount' => $requestPaymentGuide->amount_debited,
				'time_created' => now()->timestamp,
				'time_updated' => now()->timestamp
			]);
			
			if (!$cashout) {
				throw new BusinessException('Lỗi không tạo được bản ghi cashout');
			}

			$wasUpdateCreated = RequestPaymentGuide::query()->where('id', $requestPaymentGuide->id)
				->where('status_cashout', CollectDebtEnum::RPG_STT_CASHOUT_CREATING)
				->update([
					'status_cashout' => CollectDebtEnum::RPG_STT_CASHOUT_CREATED,
					'time_updated' => now()->timestamp,
				]);

			if (!$wasUpdateCreated) {
				throw new BusinessException('Lỗi không cập nhật được trạng thái yc cashout');
			}

			DB::commit();

			return $requestPaymentGuide;
		} catch (\Throwable $th) {
			DB::rollBack();

			$wasUpdateNew = $requestPaymentGuide->where([
				'id' => $requestPaymentGuide->id, 
				'status_cashout' => CollectDebtEnum::RPG_STT_CASHOUT_CREATING
			])
			->update([
				'status_cashout' => CollectDebtEnum::RPG_STT_CASHOUT_UNCREATE,
				'time_updated' => now()->timestamp
			]);

			Log::info("Error create new cashout", ['line' => $th->getLine(), 'message' => $th->getMessage()]);
			throw $th;
		}
	}
} // End class