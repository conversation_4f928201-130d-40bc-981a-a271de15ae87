<?php

use Illuminate\Support\Facades\Route;
use App\Modules\CollectDebt\Controllers\Config\ConfigController;
use App\Modules\CollectDebt\Controllers\RequestPaymentGuide\ContractController;
use App\Modules\CollectDebt\Controllers\RequestPayment\RequestPaymentController;
use App\Modules\CollectDebt\Controllers\RequestPaymentGuide\RequestPaymentGuideController;
use App\Modules\CollectDebt\Controllers\RequestPaymentReceipt\RequestPaymentReceiptController;

Route::group([
  'middleware' => [ 'nextlend.collection' ],
], function () {
	Route::get('/GetConfig', [
    'as' => 'GetConfigAction',
    'uses' => ConfigController::class . '@GetConfig'
  ]);

	Route::post('/ReportRequestPaymentGuide', [
    'as' => 'ReportRequestPaymentGuideAction',
    'uses' => RequestPaymentGuideController::class . '@ReportRequestPaymentGuide'
  ]);

	Route::get('/GetContractDetail', [
    'as' => 'GetContractDetailAction',
    'uses' => ContractController::class . '@GetContractDetail'
  ]);
	
	Route::post('/CreateRequestPaymentGuideMultiple', [
    'as' => 'CreateRequestPaymentGuideMultipleAction',
    'uses' => RequestPaymentGuideController::class . '@CreateRequestPaymentGuideMultiple'
  ]);

	Route::post('/SetStatusRequestPaymentGuide', [
    'as' => 'SetStatusRequestPaymentGuideAction',
    'uses' => RequestPaymentGuideController::class . '@SetStatusRequestPaymentGuide'
  ]);

	// route: Lấy danh sách chỉ dẫn, có phân trang
	Route::get('/GetAllRequestPaymentGuide', [
		'as' => 'GetAllRequestPaymentGuideAction',
		'uses' => RequestPaymentGuideController::class . '@GetAllRequestPaymentGuide'
	]);

	// route: Lấy danh sách chỉ dẫn, có phân trang
	Route::get('/FindRequestPaymentGuide', [
		'as' => 'FindRequestPaymentGuideAction',
		'uses' => RequestPaymentGuideController::class . '@FindRequestPaymentGuide'
	]);

	Route::get('/ExportRequestPaymentGuide', [
		'as' => 'ExportRequestPaymentGuideAction',
		'uses' => RequestPaymentGuideController::class . '@ExportRequestPaymentGuide'
	]);

	// route: Lấy danh sách lệnh trích, có phân trang
	Route::get('/GetListRequestPayment', [
		'as' => 'GetListRequestPaymentAction',
		'uses' => RequestPaymentController::class . '@GetListRequestPayment'
	]);

	// route: chi tiết lệnh trích
	Route::get('/GetDetailRequestPayment', [
		'as' => 'GetDetailRequestPaymentAction',
		'uses' => RequestPaymentController::class . '@GetDetailRequestPayment'
	]);

	// kiểm tra lệnh trích manual
	Route::get('/CheckPaymentRequestManual', [
		'as' => 'CheckPaymentRequestManualAction',
		'uses' => RequestPaymentController::class . '@CheckPaymentRequestManual'
	]);

	// route: nhận kết quả trích nợ từ mpos
	Route::post('/NotifyRequestPayment', [
		'as' => 'NotifyRequestPaymentAction',
		'uses' => RequestPaymentController::class . '@NotifyRequestPayment'
	]);

	// route: tạo bản ghi tiền về cho các kênh MPOS, VA, IB_OFF
	Route::post('/CreateRequestPaymentReceipt', [
		'as' => 'CreateRequestPaymentReceiptAction',
		'uses' => RequestPaymentReceiptController::class . '@CreateRequestPaymentReceipt'
	]);

	// Get all lịch sử tiền về
	Route::any('/GetAllRequestPaymentReceipt', [
		'as' => 'GetAllRequestPaymentReceiptAction',
		'uses' => RequestPaymentReceiptController::class . '@GetAllRequestPaymentReceipt'
	]);

	Route::post('/CancelRequestPaymentManual', [
		'as' => 'CancelRequestPaymentManualAction',
		'uses' => RequestPaymentController::class . '@CancelRequestPaymentManual'
	]);
});

// Group route job
Route::group([

], function () {
	Route::any('/jobs', function () {
		return view('jobs.index', [
			'baseUrl' => config('app.url')
		]);
	});

	// job: cập nhật merchant vào yc thu hộ
	Route::any('/AttachMcRequestPaymentGuide', [
		'as' => 'AttachMcRequestPaymentGuideAction',
		'uses' => RequestPaymentGuideController::class . '@AttachMcRequestPaymentGuide'
	]);

	// job: tạo lệnh trích từ nguồn tiền về
	Route::any('/MakeRequestPaymentFromReceipt', [
		'as' => 'MakeRequestPaymentFromReceiptAction',
		'uses' => RequestPaymentReceiptController::class . '@MakeRequestPaymentFromReceipt'
	]);

	// job: tạo lệnh trích từ chỉ dẫn
  Route::any('/ApproveRequestPaymentGuide', [
    'as' => 'ApproveRequestPaymentGuideAction',
    'uses' => RequestPaymentGuideController::class . '@ApproveRequestPaymentGuide'
  ]);

	// job: gửi lệnh trích sang mpos
  Route::any('/SendRequestPayment', [
    'as' => 'SendRequestPaymentAction',
    'uses' => RequestPaymentController::class . '@SendRequestPayment'
  ]);

	// job: kiểm tra lệnh trích
  Route::any('/CheckRequestPayment', [
    'as' => 'CheckRequestPaymentAction',
    'uses' => RequestPaymentController::class . '@CheckRequestPayment'
  ]);

	// job: cutoff lệnh trích -> về trạng thái cuối cùng
	Route::any('/CutOffRequestPayment', [
    'as' => 'CutOffRequestPaymentAction',
    'uses' => RequestPaymentController::class . '@CutOffRequestPayment'
  ]);

	// đọc log do bên service bắn về, sau đó có được mã lệnh trích thì lại gọi check sang mpos
	Route::any('/HandleNotiResultRequestPayment', [
    'as' => 'HandleNotiResultRequestPaymentAction',
    'uses' => RequestPaymentController::class . '@HandleNotiResultRequestPayment'
  ]);

	// job: đóng yc trích nợ (call về api core)
	Route::any('/CloseRequestPaymentGuide', [
		'as' => 'CloseRequestPaymentGuideAction',
		'uses' => RequestPaymentGuideController::class . '@CloseRequestPaymentGuide'
	]);

	// job: cancel lệnh mpos khi có nguồn tiền khác về trước
	Route::any('/ForceCancelRequestPaymentMpos', [
		'as' => 'ForceCancelRequestPaymentMposAction',
		'uses' => RequestPaymentController::class . '@ForceCancelRequestPaymentMpos'
	]);

	// job: Tạo bản ghi cashout
	Route::any('/CreateRequestPaymentCashout', [
		'as' => 'CreateRequestPaymentCashoutAction',
		'uses' => RequestPaymentController::class . '@CreateRequestPaymentCashout'
	]);

	// Đẩy bản ghi sang đối tác
	Route::any('/PushCashout', [
		'as' => 'PushCashoutAction',
		'uses' => RequestPaymentController::class . '@PushCashout'
	]);
});



