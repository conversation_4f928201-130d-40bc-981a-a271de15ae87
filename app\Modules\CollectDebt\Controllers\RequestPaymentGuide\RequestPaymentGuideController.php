<?php

namespace App\Modules\CollectDebt\Controllers\RequestPaymentGuide;


use Illuminate\Http\Request;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Resources\RequestPaymentResourceCollection;
use App\Modules\CollectDebt\Resources\RequestPaymentGuideResourceCollection;
use App\Modules\CollectDebt\Requests\RequestPaymentGuide\ExportRequestPaymentGuideRequest;
use App\Modules\CollectDebt\Requests\RequestPaymentGuide\GetAllRequestPaymentGuideRequest;
use App\Modules\CollectDebt\Requests\RequestPaymentGuide\ReportRequestPaymentGuideRequest;
use App\Modules\CollectDebt\Requests\RequestPaymentGuide\SetStatusRequestPaymentGuideRequest;
use App\Modules\CollectDebt\Requests\RequestPaymentGuide\CreateRequestPaymentGuideMultipleRequest;
use App\Modules\CollectDebt\Actions\RequestPaymentGuide\FindRequestPaymentGuideAction\FindRequestPaymentGuideAction;
use App\Modules\CollectDebt\Actions\RequestPaymentGuide\CloseRequestPaymentGuideAction\CloseRequestPaymentGuideAction;
use App\Modules\CollectDebt\Actions\RequestPaymentGuide\GetAllRequestPaymentGuideAction\ExportRequestPaymentGuideAction;
use App\Modules\CollectDebt\Actions\RequestPaymentGuide\GetAllRequestPaymentGuideAction\GetAllRequestPaymentGuideAction;
use App\Modules\CollectDebt\Actions\RequestPaymentGuide\ReportRequestPaymentGuideAction\ReportRequestPaymentGuideAction;
use App\Modules\CollectDebt\Actions\RequestPaymentGuide\ApproveRequestPaymentGuideAction\ApproveRequestPaymentGuideAction;
use App\Modules\CollectDebt\Actions\RequestPaymentGuide\AttachMcRequestPaymentGuideAction\AttachMcRequestPaymentGuideAction;
use App\Modules\CollectDebt\Actions\RequestPaymentGuide\SetStatusRequestPaymentGuideAction\SetStatusRequestPaymentGuideAction;
use App\Modules\CollectDebt\Actions\RequestPaymentGuide\StatisticRequestPaymentGuideAction\StatisticRequestPaymentGuideAction;
use App\Modules\CollectDebt\Actions\RequestPaymentGuide\CreateRequestPaymentGuideBulkAction\CreateRequestPaymentGuideMultipleAction;

class RequestPaymentGuideController extends Controller
{
  public function ReportRequestPaymentGuide(ReportRequestPaymentGuideRequest $request)
  {
    $result = app(ReportRequestPaymentGuideAction::class)->run($request);
    return $this->successResponse($result);
  }

	public function CreateRequestPaymentGuideMultiple(CreateRequestPaymentGuideMultipleRequest $request)
  {
		$result = app(CreateRequestPaymentGuideMultipleAction::class)->run($request);
		return $this->successResponse($result);
  }

  public function AttachMcRequestPaymentGuide()
  {
		$result = app(AttachMcRequestPaymentGuideAction::class)->run();
		return $this->successResponse($result);
  }

  public function SetStatusRequestPaymentGuide(SetStatusRequestPaymentGuideRequest $request)
  {
		$result = app(SetStatusRequestPaymentGuideAction::class)->run($request);
		return $this->successResponse($result);
  }

  public function ApproveRequestPaymentGuide()
  {
		$result = app(ApproveRequestPaymentGuideAction::class)->run();
		return $this->successResponse(['result' => $result]);
  }

  public function FindRequestPaymentGuide(Request $request)
  {
		$result = app(FindRequestPaymentGuideAction::class)->run($request); 
		return $this->successResponse($result);
  }
	
  public function GetAllRequestPaymentGuide(GetAllRequestPaymentGuideRequest $request)
  {
		$requestPaymentPaginate = app(GetAllRequestPaymentGuideAction::class)->run($request);
		$resource = new RequestPaymentGuideResourceCollection($requestPaymentPaginate);
		$response = $resource->toArray($request);
		$statistic = app(StatisticRequestPaymentGuideAction::class)->run($request->toDTO());
		$response['statistic'] = [
			'total_request' => $response['meta']['total'] ?? 0,
			'total_amount_request' => optional($statistic)->total_amount_request ?? 0,
			'total_amount_success' => optional($statistic)->total_amount_success ?? 0,
		];

		return $this->successResponse($response);
  }

	public function CloseRequestPaymentGuide()
  {
		$result = app(CloseRequestPaymentGuideAction::class)->run();
		return $this->successResponse($result);
  }

	public function ExportRequestPaymentGuide(ExportRequestPaymentGuideRequest $request)
  {
		$result = app(ExportRequestPaymentGuideAction::class)->run($request);
		return $this->successResponse($result);
  }
} // End class